import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface RaffleItem {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  endDate: Date;
  value: string;
  rank: number;
}

@Component({
  selector: 'app-raffle-platform',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './raffle-platform.component.html',
  styleUrls: ['./raffle-platform.component.css']
})
export class RafflePlatformComponent {
  
  raffleItems: RaffleItem[] = [
    {
      id: 1,
      title: 'Tesla Model S Plaid',
      description: 'Brand new 2024 Tesla Model S Plaid - The ultimate electric luxury sedan with 1,020 horsepower, 0-60 in 1.99 seconds, and 405-mile range. Includes Full Self-Driving capability, premium interior, and all available upgrades.',
      imageUrl: 'https://images.unsplash.com/photo-1617788138017-80ad40651399?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      price: 100,
      totalTickets: 2000,
      soldTickets: 1847,
      endDate: new Date('2024-08-15T23:59:59'),
      value: '$135,000',
      rank: 1
    },
    {
      id: 2,
      title: 'SUNJOY 13x15 Premium Wooden Gazebo',
      description: 'Best-selling SUNJOY 13x15 Brown 2-tier Wooden Frame Hardtop Patio Gazebo with ceiling hook. Features sturdy cedar construction, weather-resistant steel roof, dual netting rods for insect protection, and spacious design perfect for outdoor entertaining.',
      imageUrl: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      price: 75,
      totalTickets: 1500,
      soldTickets: 1234,
      endDate: new Date('2024-08-10T23:59:59'),
      value: '$2,770',
      rank: 2
    },
    {
      id: 3,
      title: 'Luxury Tech & Watch Bundle',
      description: 'Premium collection featuring: Rolex Submariner Date (41mm), Apple Watch Ultra 2, MacBook Pro 16" M3 Max, iPhone 15 Pro Max 1TB, AirPods Max, and iPad Pro 12.9" M2. The ultimate luxury tech lifestyle package.',
      imageUrl: 'https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
      price: 50,
      totalTickets: 1000,
      soldTickets: 823,
      endDate: new Date('2024-08-05T23:59:59'),
      value: '$45,000',
      rank: 3
    }
  ];

  get topThreeRaffles() {
    return this.raffleItems.sort((a, b) => a.rank - b.rank);
  }

  getProgressPercentage(item: RaffleItem): number {
    return Math.round((item.soldTickets / item.totalTickets) * 100);
  }

  getTimeRemaining(endDate: Date): string {
    const now = new Date();
    const diff = endDate.getTime() - now.getTime();
    
    if (diff <= 0) {
      return 'Ended';
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  enterRaffle(item: RaffleItem) {
    // This would integrate with payment processing
    console.log(`Entering raffle for: ${item.title}`);
    alert(`Redirecting to payment for ${item.title} - $${item.price} per ticket`);
  }
}
