import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface RaffleItem {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  totalTickets: number;
  soldTickets: number;
  endDate: Date;
  value: string;
  rank: number;
}

@Component({
  selector: 'app-raffle-platform',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './raffle-platform.component.html',
  styleUrls: ['./raffle-platform.component.css']
})
export class RafflePlatformComponent {
  
  raffleItems: RaffleItem[] = [
    {
      id: 1,
      title: 'Tesla Model 3',
      description: 'Brand new 2024 Tesla Model 3 - Fully loaded with autopilot',
      imageUrl: 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=800&h=600&fit=crop',
      price: 100,
      totalTickets: 2000,
      soldTickets: 1847,
      endDate: new Date('2024-08-15T23:59:59'),
      value: '$45,000',
      rank: 1
    },
    {
      id: 2,
      title: '$50,000 Cash Prize',
      description: 'Tax-free cash prize - No strings attached',
      imageUrl: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=600&fit=crop',
      price: 75,
      totalTickets: 1500,
      soldTickets: 1234,
      endDate: new Date('2024-08-10T23:59:59'),
      value: '$50,000',
      rank: 2
    },
    {
      id: 3,
      title: 'Luxury Watch Collection',
      description: 'Rolex Submariner + Omega Speedmaster + Apple Watch Ultra',
      imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=800&h=600&fit=crop',
      price: 50,
      totalTickets: 1000,
      soldTickets: 823,
      endDate: new Date('2024-08-05T23:59:59'),
      value: '$25,000',
      rank: 3
    }
  ];

  get topThreeRaffles() {
    return this.raffleItems.sort((a, b) => a.rank - b.rank);
  }

  getProgressPercentage(item: RaffleItem): number {
    return Math.round((item.soldTickets / item.totalTickets) * 100);
  }

  getTimeRemaining(endDate: Date): string {
    const now = new Date();
    const diff = endDate.getTime() - now.getTime();
    
    if (diff <= 0) {
      return 'Ended';
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  enterRaffle(item: RaffleItem) {
    // This would integrate with payment processing
    console.log(`Entering raffle for: ${item.title}`);
    alert(`Redirecting to payment for ${item.title} - $${item.price} per ticket`);
  }
}
