<!-- Premium Raffle Platform -->
<div class="min-h-screen bg-gradient-to-br from-red-50 via-yellow-50 to-red-100">

  <!-- Header -->
  <header class="bg-white shadow-xl border-b-4 border-gradient-to-r from-red-600 to-yellow-500">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gradient-to-br from-red-600 via-red-700 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
            <!-- Target SVG Icon -->
            <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent">WinBig</h1>
            <p class="text-sm text-red-600 font-medium">Premium Raffles</p>
          </div>
        </div>

        <!-- User Actions -->
        <div class="flex items-center space-x-4">
          <button class="text-gray-700 hover:text-red-600 font-medium transition-colors">Sign In</button>
          <button class="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-2 rounded-lg font-medium hover:from-red-700 hover:to-red-800 transition-all shadow-lg">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="bg-gradient-to-r from-red-600 via-red-700 to-yellow-600 text-white py-16 relative overflow-hidden">
    <!-- Gold accent patterns -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-10 left-10 w-32 h-32 border-2 border-yellow-300 rounded-full"></div>
      <div class="absolute bottom-10 right-10 w-24 h-24 border border-yellow-400 rounded-full"></div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
      <h2 class="text-5xl font-bold mb-6">Win Life-Changing Prizes</h2>
      <p class="text-xl mb-8 text-red-100 max-w-3xl mx-auto">
        Three incredible prizes. One lucky winner each. Enter now for your chance to win big.
      </p>
      <div class="flex justify-center space-x-6 text-sm">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Live Draws</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Secure Payments</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Instant Delivery</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Top 3 Prizes -->
  <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-red-50/30">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <div class="flex justify-center mb-6">
          <!-- Trophy SVG -->
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center shadow-lg">
            <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20.38C20.77 4 21.08 4.31 21.08 4.69C21.08 4.86 21.02 5.02 20.91 5.14L19.64 6.41C19.78 6.85 19.85 7.32 19.85 7.81C19.85 10.79 17.43 13.21 14.45 13.21C14.04 13.21 13.64 13.16 13.26 13.07L12 14.34L10.74 13.07C10.36 13.16 9.96 13.21 9.55 13.21C6.57 13.21 4.15 10.79 4.15 7.81C4.15 7.32 4.22 6.85 4.36 6.41L3.09 5.14C2.97 5.02 2.92 4.86 2.92 4.69C2.92 4.31 3.23 4 3.62 4H7ZM9 3V4H15V3H9Z"/>
            </svg>
          </div>
        </div>
        <h3 class="text-5xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent mb-6">Top 3 Prizes</h3>
        <p class="text-gray-700 text-xl max-w-3xl mx-auto leading-relaxed">Three incredible prizes. One lucky winner each. Enter now for your chance to win big.</p>

        <!-- Prize Stats -->
        <div class="flex justify-center items-center space-x-8 mt-8 text-sm">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-gradient-to-r from-red-500 to-yellow-500 rounded-full"></div>
            <span class="text-gray-600 font-medium">Total Value: $182,770</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
            <span class="text-gray-600 font-medium">3 Lucky Winners</span>
          </div>
        </div>
      </div>

      <!-- Prize Cards Grid - Inspired by Sunjoy's clean layout -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-10 max-w-6xl mx-auto">
        <div *ngFor="let item of topThreeRaffles; let i = index"
             class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-200 relative group"
             [class]="item.rank === 1 ? 'lg:scale-105 border-yellow-300' : item.rank === 2 ? 'border-green-300' : 'border-blue-300'">

          <!-- Rank Badge -->
          <div class="absolute top-4 left-4 z-20 flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-bold shadow-xl backdrop-blur-sm"
               [class]="item.rank === 1 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900' : item.rank === 2 ? 'bg-gradient-to-r from-green-400 to-green-500 text-green-900' : 'bg-gradient-to-r from-blue-400 to-blue-500 text-blue-900'">
            <!-- Medal SVG -->
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
            </svg>
            <span>#{{ item.rank }} PRIZE</span>
          </div>

          <!-- Prize Image -->
          <div class="h-80 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden relative group">
            <img [src]="item.imageUrl"
                 [alt]="item.title"
                 class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

            <!-- Image Overlay for Better Visual Hierarchy -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <!-- Prize Category Badge -->
            <div class="absolute top-4 right-4 z-20 px-3 py-2 rounded-full text-xs font-bold shadow-xl backdrop-blur-sm"
                 [class]="item.rank === 1 ? 'bg-red-500/90 text-white' : item.rank === 2 ? 'bg-green-600/90 text-white' : 'bg-blue-600/90 text-white'">
              <span *ngIf="item.rank === 1">GRAND PRIZE</span>
              <span *ngIf="item.rank === 2">OUTDOOR LIVING</span>
              <span *ngIf="item.rank === 3">TECH BUNDLE</span>
            </div>

            <!-- Prize Value Overlay -->
            <div class="absolute bottom-4 left-4 z-20 px-4 py-2 rounded-xl backdrop-blur-sm bg-white/90 shadow-lg">
              <div class="text-2xl font-black bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent">{{ item.value }}</div>
              <div class="text-xs text-gray-600 font-medium uppercase tracking-wide">Total Value</div>
            </div>
          </div>

          <!-- Card Content -->
          <div class="p-8 bg-gradient-to-b from-white to-gray-50/50">
            <!-- Title -->
            <h4 class="text-2xl font-bold text-gray-900 mb-4 text-center leading-tight">{{ item.title }}</h4>

            <!-- Enhanced Description -->
            <div class="mb-6">
              <p class="text-gray-700 text-sm leading-relaxed text-center mb-6">{{ item.description }}</p>

              <!-- Key Features -->
              <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 border border-gray-200">
                <div class="grid grid-cols-1 gap-3 text-xs">
                  <div class="flex items-center justify-center space-x-6" *ngIf="item.rank === 1">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">Full Self-Driving</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">Premium Interior</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-center space-x-6" *ngIf="item.rank === 2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">Cedar Construction</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">Weather Resistant</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-center space-x-6" *ngIf="item.rank === 3">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">6 Premium Items</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span class="text-gray-700 font-medium">Latest Models</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Clean Progress Bar - Sunjoy Style -->
            <div class="mb-8">
              <div class="flex justify-between items-center text-sm mb-3">
                <div class="flex items-center space-x-2">
                  <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M22 10V6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V10C3.1 10 4 10.9 4 12C4 13.1 3.1 14 2 14V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V14C20.9 14 20 13.1 20 12C20 10.9 20.9 10 22 10Z"/>
                  </svg>
                  <span class="text-gray-700 font-medium">{{ item.soldTickets.toLocaleString() }}/{{ item.totalTickets.toLocaleString() }} sold</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-lg font-bold text-gray-900">{{ getProgressPercentage(item) }}%</span>
                  <span class="text-xs text-gray-500 font-medium uppercase tracking-wide">Complete</span>
                </div>
              </div>

              <div class="relative mb-3">
                <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div class="bg-gradient-to-r from-red-500 to-yellow-500 h-3 rounded-full transition-all duration-500"
                       [style.width.%]="getProgressPercentage(item)">
                  </div>
                </div>
              </div>

              <!-- Clean urgency indicator -->
              <div class="text-center">
                <span class="text-xs text-gray-600">{{ item.totalTickets - item.soldTickets }} tickets remaining</span>
                <span class="ml-2 text-xs font-bold"
                      [class]="getProgressPercentage(item) > 80 ? 'text-red-600' : getProgressPercentage(item) > 60 ? 'text-orange-600' : 'text-green-600'">
                  <span *ngIf="getProgressPercentage(item) > 80">Almost Sold Out!</span>
                  <span *ngIf="getProgressPercentage(item) > 60 && getProgressPercentage(item) <= 80">Selling Fast!</span>
                  <span *ngIf="getProgressPercentage(item) <= 60">Available Now</span>
                </span>
              </div>
            </div>

            <!-- Clean Price and Time - Sunjoy Style -->
            <div class="grid grid-cols-2 gap-6 mb-8">
              <!-- Price Section -->
              <div class="bg-gray-50 rounded-lg p-4 text-center border border-gray-200">
                <div class="text-2xl font-bold text-gray-900 mb-1">${{ item.price }}</div>
                <div class="text-sm text-gray-600 font-medium">per ticket</div>
              </div>

              <!-- Time Section -->
              <div class="bg-gray-50 rounded-lg p-4 text-center border border-gray-200">
                <div class="text-lg font-bold text-gray-900 mb-1">{{ getTimeRemaining(item.endDate) }}</div>
                <div class="text-sm text-gray-600 font-medium">remaining</div>
              </div>
            </div>

            <!-- Clean Enter Button - Sunjoy Style -->
            <button (click)="enterRaffle(item)"
                    class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white py-4 rounded-lg font-semibold hover:from-red-700 hover:to-red-800 transition-all duration-200 text-base shadow-md hover:shadow-lg">
              Enter Raffle Now
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works -->
  <section class="py-16 bg-gradient-to-br from-white via-red-50/30 to-yellow-50/30">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h3 class="text-3xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent mb-12">How It Works</h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-red-100 to-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-red-200">
            <!-- Ticket SVG -->
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 10V6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V10C3.1 10 4 10.9 4 12C4 13.1 3.1 14 2 14V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V14C20.9 14 20 13.1 20 12C20 10.9 20.9 10 22 10ZM13 17.5H11V16.5H13V17.5ZM13 15.5H11V14.5H13V15.5ZM13 13.5H11V12.5H13V13.5ZM13 11.5H11V10.5H13V11.5ZM13 9.5H11V8.5H13V9.5ZM13 7.5H11V6.5H13V7.5Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">1. Buy Tickets</h4>
          <p class="text-gray-600">Choose your prize and purchase tickets. More tickets = better chances!</p>
        </div>

        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-red-100 to-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-red-200">
            <!-- Clock SVG -->
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2ZM17 13H11V7H12.5V11.5H17V13Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">2. Wait for Draw</h4>
          <p class="text-gray-600">Sit back and wait for the draw date. We'll notify you when it's time!</p>
        </div>

        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-yellow-300">
            <!-- Trophy SVG -->
            <svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20.38C20.77 4 21.08 4.31 21.08 4.69C21.08 4.86 21.02 5.02 20.91 5.14L19.64 6.41C19.78 6.85 19.85 7.32 19.85 7.81C19.85 10.79 17.43 13.21 14.45 13.21C14.04 13.21 13.64 13.16 13.26 13.07L12 14.34L10.74 13.07C10.36 13.16 9.96 13.21 9.55 13.21C6.57 13.21 4.15 10.79 4.15 7.81C4.15 7.32 4.22 6.85 4.36 6.41L3.09 5.14C2.97 5.02 2.92 4.86 2.92 4.69C2.92 4.31 3.23 4 3.62 4H7ZM9 3V4H15V3H9Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">3. Win Big</h4>
          <p class="text-gray-600">Winners are selected randomly and fairly. Could be you!</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-red-600 via-red-700 to-yellow-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
              </svg>
            </div>
            <span class="text-xl font-bold bg-gradient-to-r from-red-400 to-yellow-400 bg-clip-text text-transparent">WinBig</span>
          </div>
          <p class="text-gray-400">The most trusted raffle platform for premium products and life-changing prizes.</p>
        </div>
        <div>
          <h5 class="font-bold mb-4">Platform</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">How it Works</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Fair Play</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Support</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Terms</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Connect</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Twitter</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Instagram</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Discord</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2025 WinBig Premium Raffles. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
