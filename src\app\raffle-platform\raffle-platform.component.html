<!-- Premium Raffle Platform -->
<div class="min-h-screen bg-gradient-to-br from-red-50 via-yellow-50 to-red-100">

  <!-- Header -->
  <header class="bg-white shadow-xl border-b-4 border-gradient-to-r from-red-600 to-yellow-500">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 bg-gradient-to-br from-red-600 via-red-700 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
            <!-- Target SVG Icon -->
            <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent">WinBig</h1>
            <p class="text-sm text-red-600 font-medium">Premium Raffles</p>
          </div>
        </div>

        <!-- User Actions -->
        <div class="flex items-center space-x-4">
          <button class="text-gray-700 hover:text-red-600 font-medium transition-colors">Sign In</button>
          <button class="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-2 rounded-lg font-medium hover:from-red-700 hover:to-red-800 transition-all shadow-lg">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="bg-gradient-to-r from-red-600 via-red-700 to-yellow-600 text-white py-16 relative overflow-hidden">
    <!-- Gold accent patterns -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute top-10 left-10 w-32 h-32 border-2 border-yellow-300 rounded-full"></div>
      <div class="absolute bottom-10 right-10 w-24 h-24 border border-yellow-400 rounded-full"></div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
      <h2 class="text-5xl font-bold mb-6">Win Life-Changing Prizes</h2>
      <p class="text-xl mb-8 text-red-100 max-w-3xl mx-auto">
        Three incredible prizes. One lucky winner each. Enter now for your chance to win big.
      </p>
      <div class="flex justify-center space-x-6 text-sm">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Live Draws</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Secure Payments</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-yellow-400 rounded-full shadow-lg"></div>
          <span>Instant Delivery</span>
        </div>
      </div>
    </div>
  </section>

  <!-- Top 3 Prizes -->
  <section class="py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <div class="flex justify-center mb-4">
          <!-- Trophy SVG -->
          <svg class="w-12 h-12 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20.38C20.77 4 21.08 4.31 21.08 4.69C21.08 4.86 21.02 5.02 20.91 5.14L19.64 6.41C19.78 6.85 19.85 7.32 19.85 7.81C19.85 10.79 17.43 13.21 14.45 13.21C14.04 13.21 13.64 13.16 13.26 13.07L12 14.34L10.74 13.07C10.36 13.16 9.96 13.21 9.55 13.21C6.57 13.21 4.15 10.79 4.15 7.81C4.15 7.32 4.22 6.85 4.36 6.41L3.09 5.14C2.97 5.02 2.92 4.86 2.92 4.69C2.92 4.31 3.23 4 3.62 4H7ZM9 3V4H15V3H9ZM12 12L13.5 10.5C14.5 10.5 15.35 9.65 15.35 8.65C15.35 7.65 14.5 6.8 13.5 6.8C12.5 6.8 11.65 7.65 11.65 8.65C11.65 9.65 12.5 10.5 13.5 10.5L12 12ZM8.5 6.8C7.5 6.8 6.65 7.65 6.65 8.65C6.65 9.65 7.5 10.5 8.5 10.5C9.5 10.5 10.35 9.65 10.35 8.65C10.35 7.65 9.5 6.8 8.5 6.8ZM12 15L10.22 16.78C9.55 17.45 8.45 17.45 7.78 16.78L6 15H12ZM18 15L16.22 16.78C15.55 17.45 14.45 17.45 13.78 16.78L12 15H18ZM12 18L13.78 19.78C14.45 20.45 15.55 20.45 16.22 19.78L18 18H12ZM6 18L7.78 19.78C8.45 20.45 9.55 20.45 10.22 19.78L12 18H6Z"/>
          </svg>
        </div>
        <h3 class="text-4xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent mb-4">Top 3 Prizes</h3>
        <p class="text-gray-700 text-lg">Three life-changing prizes. Enter now for your chance to win.</p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div *ngFor="let item of topThreeRaffles; let i = index"
             class="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-2"
             [class]="item.rank === 1 ? 'border-yellow-400 lg:scale-110 lg:-mt-8' : item.rank === 2 ? 'border-gray-300' : 'border-orange-300'">

          <!-- Rank Badge -->
          <div class="relative">
            <div class="absolute top-4 left-4 z-10 flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-bold shadow-lg"
                 [class]="item.rank === 1 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900' : item.rank === 2 ? 'bg-gradient-to-r from-gray-300 to-gray-400 text-gray-800' : 'bg-gradient-to-r from-orange-400 to-orange-500 text-orange-900'">
              <!-- Medal SVG -->
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
              </svg>
              <span>#{{ item.rank }} PRIZE</span>
            </div>

            <!-- Prize Image -->
            <div class="h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
              <img [src]="item.imageUrl"
                   [alt]="item.title"
                   class="w-full h-full object-cover">
            </div>
          </div>

          <!-- Card Content -->
          <div class="p-6 bg-gradient-to-b from-white to-red-50/30">
            <!-- Prize Value -->
            <div class="text-center mb-4">
              <div class="text-3xl font-black bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent mb-1">{{ item.value }}</div>
              <div class="text-sm text-gray-500 uppercase tracking-wide font-medium">Prize Value</div>
            </div>

            <h4 class="text-xl font-bold text-gray-900 mb-2 text-center">{{ item.title }}</h4>
            <p class="text-gray-600 mb-6 text-center text-sm">{{ item.description }}</p>

            <!-- Progress Bar -->
            <div class="mb-6">
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>{{ item.soldTickets }}/{{ item.totalTickets }} tickets sold</span>
                <span class="font-bold">{{ getProgressPercentage(item) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                <div class="bg-gradient-to-r from-red-500 to-yellow-500 h-3 rounded-full transition-all duration-500 shadow-sm"
                     [style.width.%]="getProgressPercentage(item)"></div>
              </div>
            </div>

            <!-- Price and Time -->
            <div class="flex justify-between items-center mb-6 bg-gradient-to-r from-red-50 to-yellow-50 rounded-xl p-4 border border-red-100">
              <div class="text-center">
                <div class="text-2xl font-bold bg-gradient-to-r from-red-600 to-red-700 bg-clip-text text-transparent">${{ item.price }}</div>
                <div class="text-sm text-gray-600 font-medium">per ticket</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-bold text-gray-900">{{ getTimeRemaining(item.endDate) }}</div>
                <div class="text-sm text-gray-600 font-medium">remaining</div>
              </div>
            </div>

            <!-- Enter Button -->
            <button (click)="enterRaffle(item)"
                    class="w-full bg-gradient-to-r from-red-600 via-red-700 to-yellow-600 text-white py-4 rounded-xl font-bold hover:from-red-700 hover:via-red-800 hover:to-yellow-700 transition-all transform hover:scale-105 duration-200 text-lg shadow-lg hover:shadow-xl">
              <div class="flex items-center justify-center space-x-2">
                <!-- Lightning SVG -->
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 2v11h3v9l7-12h-4l4-8z"/>
                </svg>
                <span>Enter Raffle Now</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works -->
  <section class="py-16 bg-gradient-to-br from-white via-red-50/30 to-yellow-50/30">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h3 class="text-3xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent mb-12">How It Works</h3>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-red-100 to-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-red-200">
            <!-- Ticket SVG -->
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 10V6C22 4.9 21.1 4 20 4H4C2.9 4 2 4.9 2 6V10C3.1 10 4 10.9 4 12C4 13.1 3.1 14 2 14V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V14C20.9 14 20 13.1 20 12C20 10.9 20.9 10 22 10ZM13 17.5H11V16.5H13V17.5ZM13 15.5H11V14.5H13V15.5ZM13 13.5H11V12.5H13V13.5ZM13 11.5H11V10.5H13V11.5ZM13 9.5H11V8.5H13V9.5ZM13 7.5H11V6.5H13V7.5Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">1. Buy Tickets</h4>
          <p class="text-gray-600">Choose your prize and purchase tickets. More tickets = better chances!</p>
        </div>

        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-red-100 to-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-red-200">
            <!-- Clock SVG -->
            <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2ZM17 13H11V7H12.5V11.5H17V13Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">2. Wait for Draw</h4>
          <p class="text-gray-600">Sit back and wait for the draw date. We'll notify you when it's time!</p>
        </div>

        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-yellow-300">
            <!-- Trophy SVG -->
            <svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20.38C20.77 4 21.08 4.31 21.08 4.69C21.08 4.86 21.02 5.02 20.91 5.14L19.64 6.41C19.78 6.85 19.85 7.32 19.85 7.81C19.85 10.79 17.43 13.21 14.45 13.21C14.04 13.21 13.64 13.16 13.26 13.07L12 14.34L10.74 13.07C10.36 13.16 9.96 13.21 9.55 13.21C6.57 13.21 4.15 10.79 4.15 7.81C4.15 7.32 4.22 6.85 4.36 6.41L3.09 5.14C2.97 5.02 2.92 4.86 2.92 4.69C2.92 4.31 3.23 4 3.62 4H7ZM9 3V4H15V3H9Z"/>
            </svg>
          </div>
          <h4 class="text-xl font-bold text-gray-900 mb-2">3. Win Big</h4>
          <p class="text-gray-600">Winners are selected randomly and fairly. Could be you!</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <span class="text-2xl">🎯</span>
            <span class="text-xl font-bold">RafflePro</span>
          </div>
          <p class="text-gray-400">The most trusted raffle platform for premium products.</p>
        </div>
        <div>
          <h5 class="font-bold mb-4">Platform</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">How it Works</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Fair Play</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Support</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Terms</a></li>
          </ul>
        </div>
        <div>
          <h5 class="font-bold mb-4">Connect</h5>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">Twitter</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Instagram</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Discord</a></li>
          </ul>
        </div>
      </div>
      <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2024 RafflePro. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
