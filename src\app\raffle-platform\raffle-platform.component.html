<!-- <PERSON><PERSON>-<PERSON> Raffle Platform -->
<div class="min-h-screen bg-white">

  <!-- Mobile Menu <PERSON> (Hidden on Desktop) -->
  <div class="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-gradient-to-br from-red-600 to-yellow-600 rounded-lg flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
          </svg>
        </div>
        <span class="text-lg font-bold text-gray-900">WinBig</span>
      </div>
      <button class="p-2">
        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Desktop Header - Sunjoy Style -->
  <header class="hidden lg:block bg-white border-b border-gray-200">
    <!-- Top Bar -->
    <div class="bg-gray-50 border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-2 text-sm">
          <div class="flex items-center space-x-6">
            <span class="text-gray-600">🎯 Premium Raffle Platform</span>
            <span class="text-gray-600">🔒 Secure & Fair</span>
          </div>
          <div class="flex items-center space-x-4">
            <a href="#" class="text-gray-600 hover:text-red-600 transition-colors">Help</a>
            <a href="#" class="text-gray-600 hover:text-red-600 transition-colors">Contact</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Header -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <!-- Logo -->
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-red-600 to-yellow-600 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
            </svg>
          </div>
          <div>
            <h1 class="text-xl font-bold text-gray-900">WinBig</h1>
            <p class="text-xs text-gray-500">Premium Raffles</p>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="hidden lg:flex items-center space-x-8">
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Raffles</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Winners</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">How It Works</a>
          <a href="#" class="text-gray-700 hover:text-red-600 font-medium transition-colors">Support</a>
        </nav>

        <!-- User Actions -->
        <div class="flex items-center space-x-4">
          <button class="text-gray-700 hover:text-red-600 font-medium transition-colors">Sign In</button>
          <button class="bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700 transition-colors">
            Sign Up
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Banner - Sunjoy Style -->
  <section class="bg-gradient-to-r from-red-600 to-red-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-12 lg:py-20 text-center">
        <h1 class="text-3xl lg:text-5xl font-bold mb-4 lg:mb-6">Win Life-Changing Prizes</h1>
        <p class="text-lg lg:text-xl mb-8 text-red-100 max-w-3xl mx-auto">
          Three incredible prizes. One lucky winner each. Enter now for your chance to win big.
        </p>

        <!-- Trust Indicators - Sunjoy Style -->
        <div class="flex flex-wrap justify-center gap-4 lg:gap-8 text-sm lg:text-base">
          <div class="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
            <span>Live Draws</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
            <span>Secure Payments</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2">
            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
            <span>Instant Delivery</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Trust Bar - Sunjoy Style -->
  <section class="bg-gray-50 border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-4">
        <div class="grid grid-cols-2 lg:grid-cols-5 gap-4 text-center text-sm">
          <div class="flex flex-col items-center space-y-1">
            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span class="text-gray-600 font-medium">Free Shipping</span>
          </div>
          <div class="flex flex-col items-center space-y-1">
            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
            </svg>
            <span class="text-gray-600 font-medium">Secure Checkout</span>
          </div>
          <div class="flex flex-col items-center space-y-1">
            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span class="text-gray-600 font-medium">Fair Draws</span>
          </div>
          <div class="flex flex-col items-center space-y-1">
            <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/>
            </svg>
            <span class="text-gray-600 font-medium">24/7 Support</span>
          </div>
          <div class="flex flex-col items-center space-y-1 col-span-2 lg:col-span-1">
            <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span class="text-gray-600 font-medium">Trusted Platform</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Featured Prizes - Sunjoy Style -->
  <section class="py-12 lg:py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Section Header -->
      <div class="text-center mb-8 lg:mb-12">
        <h2 class="text-2xl lg:text-4xl font-bold text-gray-900 mb-4">Featured Prizes</h2>
        <p class="text-gray-600 text-base lg:text-lg max-w-2xl mx-auto">
          Three incredible prizes. One lucky winner each. Enter now for your chance to win.
        </p>

        <!-- Prize Stats -->
        <div class="flex flex-wrap justify-center gap-4 lg:gap-8 mt-6 text-sm">
          <div class="flex items-center space-x-2 bg-gray-50 rounded-full px-4 py-2">
            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
            <span class="text-gray-700 font-medium">Total Value: $182,770</span>
          </div>
          <div class="flex items-center space-x-2 bg-gray-50 rounded-full px-4 py-2">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-gray-700 font-medium">3 Lucky Winners</span>
          </div>
        </div>
      </div>

      <!-- Prize Grid - Sunjoy Product Style -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        <div *ngFor="let item of topThreeRaffles; let i = index"
             class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 group"
             [class]="item.rank === 1 ? 'ring-2 ring-yellow-400' : ''">

          <!-- Rank Badge - Sunjoy Style -->
          <div class="absolute top-3 left-3 z-10 px-2 py-1 rounded text-xs font-bold text-white"
               [class]="item.rank === 1 ? 'bg-yellow-500' : item.rank === 2 ? 'bg-green-500' : 'bg-blue-500'">
            #{{ item.rank }}
          </div>

          <!-- Product Image - Sunjoy Style -->
          <div class="relative overflow-hidden bg-gray-50">
            <img [src]="item.imageUrl"
                 [alt]="item.title"
                 class="w-full h-48 lg:h-64 object-cover group-hover:scale-105 transition-transform duration-300">

            <!-- Quick Add Button - Sunjoy Style -->
            <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              <button class="bg-white text-gray-900 px-4 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors">
                + Quick Enter
              </button>
            </div>
          </div>

          <!-- Product Content - Sunjoy Style -->
          <div class="p-4 lg:p-6">
            <!-- Product Title -->
            <h3 class="text-lg lg:text-xl font-bold text-gray-900 mb-2 line-clamp-2">{{ item.title }}</h3>

            <!-- Prize Value -->
            <div class="mb-4">
              <span class="text-2xl lg:text-3xl font-bold text-red-600">{{ item.value }}</span>
              <span class="text-sm text-gray-500 ml-2">Prize Value</span>
            </div>

            <!-- Progress Bar - Sunjoy Style -->
            <div class="mb-4">
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>{{ item.soldTickets.toLocaleString() }}/{{ item.totalTickets.toLocaleString() }} sold</span>
                <span class="font-medium">{{ getProgressPercentage(item) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-red-600 h-2 rounded-full transition-all duration-300"
                     [style.width.%]="getProgressPercentage(item)">
                </div>
              </div>
            </div>

            <!-- Ticket Price and Time -->
            <div class="flex justify-between items-center mb-4 text-sm">
              <div>
                <span class="text-lg font-bold text-gray-900">${{ item.price }}</span>
                <span class="text-gray-600 ml-1">per ticket</span>
              </div>
              <div class="text-gray-600">
                <span class="font-medium">{{ getTimeRemaining(item.endDate) }}</span> left
              </div>
            </div>

            <!-- Enter Button - Sunjoy Style -->
            <button (click)="enterRaffle(item)"
                    class="w-full bg-red-600 text-white py-3 rounded-md font-medium hover:bg-red-700 transition-colors duration-200">
              Enter Raffle Now
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works - Sunjoy Style -->
  <section class="py-12 lg:py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-8 lg:mb-12">
        <h2 class="text-2xl lg:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
        <p class="text-gray-600 text-base lg:text-lg">Simple, fair, and transparent raffle process</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
        <div class="text-center">
          <div class="w-12 h-12 lg:w-16 lg:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-lg lg:text-2xl font-bold text-white">1</span>
          </div>
          <h3 class="text-lg lg:text-xl font-bold text-gray-900 mb-2">Choose & Enter</h3>
          <p class="text-gray-600 text-sm lg:text-base">Select your favorite prize and purchase raffle tickets securely.</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 lg:w-16 lg:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-lg lg:text-2xl font-bold text-white">2</span>
          </div>
          <h3 class="text-lg lg:text-xl font-bold text-gray-900 mb-2">Live Draw</h3>
          <p class="text-gray-600 text-sm lg:text-base">Watch the live draw when the raffle ends. Everything is transparent.</p>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 lg:w-16 lg:h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-lg lg:text-2xl font-bold text-white">3</span>
          </div>
          <h3 class="text-lg lg:text-xl font-bold text-gray-900 mb-2">Win Big</h3>
          <p class="text-gray-600 text-sm lg:text-base">Winners are selected randomly and fairly. Could be you!</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer - Sunjoy Style -->
  <footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
        <!-- About -->
        <div>
          <h5 class="font-bold mb-4 text-white">About</h5>
          <ul class="space-y-2 text-gray-400 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">About WinBig</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">How It Works</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Fair Play</a></li>
          </ul>
        </div>

        <!-- Customer Experience -->
        <div>
          <h5 class="font-bold mb-4 text-white">Customer Experience</h5>
          <ul class="space-y-2 text-gray-400 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">FAQs</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Track Your Entry</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Winner Stories</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Support</a></li>
          </ul>
        </div>

        <!-- Terms & Conditions -->
        <div>
          <h5 class="font-bold mb-4 text-white">Terms & Conditions</h5>
          <ul class="space-y-2 text-gray-400 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">Terms of Service</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Raffle Rules</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>

        <!-- Newsletter -->
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-8 h-8 bg-gradient-to-br from-red-600 to-yellow-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
              </svg>
            </div>
            <span class="text-lg font-bold">WinBig</span>
          </div>
          <p class="text-gray-400 text-sm mb-4">Sign up for news, promotions & exclusive raffle access.</p>
          <div class="flex">
            <input type="email" placeholder="Your Email" class="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-l-md text-sm focus:outline-none focus:border-red-500">
            <button class="bg-red-600 text-white px-4 py-2 rounded-r-md text-sm hover:bg-red-700 transition-colors">
              Subscribe
            </button>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">&copy; 2025, WinBig Premium Raffles. All rights reserved.</p>
        <div class="flex space-x-4 mt-4 md:mt-0">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
            </svg>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>
</div>
