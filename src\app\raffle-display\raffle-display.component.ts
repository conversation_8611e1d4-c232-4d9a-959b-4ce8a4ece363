import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface Winner {
  rank: number;
  name: string;
  prize: string;
  emoji: string;
  rankEmoji: string;
  initials: string;
}

@Component({
  selector: 'app-raffle-display',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './raffle-display.component.html',
  styleUrl: './raffle-display.component.css'
})
export class RaffleDisplayComponent {
  winners: Winner[] = [
    {
      rank: 1,
      name: '<PERSON>',
      prize: 'iPhone 15 Pro',
      emoji: '📱',
      rankEmoji: '🥇',
      initials: 'AC'
    },
    {
      rank: 2,
      name: '<PERSON>',
      prize: 'Apple Watch',
      emoji: '⌚',
      rankEmoji: '🥈',
      initials: 'B<PERSON>'
    },
    {
      rank: 3,
      name: '<PERSON>',
      prize: 'Amazon Gift Card',
      emoji: '🎁',
      rankEmoji: '🥉',
      initials: 'CL'
    }
  ];

  getOrdinalSuffix(rank: number): string {
    const suffixes = ['st', 'nd', 'rd'];
    return suffixes[rank - 1] || 'th';
  }
}
