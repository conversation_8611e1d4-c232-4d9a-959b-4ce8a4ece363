<div class="min-h-screen bg-gradient-to-br from-red-50 via-amber-50 to-red-100 py-12 px-4 relative overflow-hidden">
  <!-- Professional Background Pattern -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute top-20 left-20 w-32 h-32 border-2 border-red-300 rounded-full"></div>
    <div class="absolute top-40 right-32 w-24 h-24 border border-yellow-400 rounded-full"></div>
    <div class="absolute bottom-32 left-40 w-20 h-20 border border-red-400 rounded-full"></div>
    <div class="absolute bottom-20 right-20 w-28 h-28 border-2 border-yellow-300 rounded-full"></div>
  </div>

  <!-- Elegant Background Decorations -->
  <div class="absolute top-16 left-16 text-4xl opacity-8 text-red-300 sparkle">🏮</div>
  <div class="absolute top-24 right-20 text-3xl opacity-8 text-yellow-400 sparkle" style="animation-delay: 1s">✨</div>
  <div class="absolute bottom-32 left-24 text-3xl opacity-8 text-red-300 sparkle" style="animation-delay: 2s">🎊</div>
  <div class="absolute bottom-20 right-16 text-2xl opacity-8 text-yellow-400 sparkle" style="animation-delay: 1.5s">🎉</div>

  <!-- Professional Header -->
  <div class="text-center mb-16 animate-fade-in relative z-10">
    <!-- Logo/Icon Area -->
    <div class="mb-8 relative">
      <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-red-600 to-red-700 rounded-full shadow-2xl mb-6">
        <span class="text-4xl">🏮</span>
      </div>
    </div>

    <!-- Main Title -->
    <h1 class="text-5xl md:text-7xl font-black gradient-text-premium text-luxury mb-6 tracking-tight">
      Lucky Draw Winners
    </h1>

    <!-- Elegant Divider -->
    <div class="flex justify-center items-center space-x-6 mb-6">
      <div class="w-24 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent"></div>
      <div class="w-3 h-3 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full shadow-lg"></div>
      <div class="w-32 h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
      <div class="w-3 h-3 bg-gradient-to-br from-red-500 to-red-600 rounded-full shadow-lg"></div>
      <div class="w-24 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent"></div>
    </div>

    <!-- Subtitle -->
    <p class="text-xl text-red-700 font-semibold tracking-wide">Celebrating Excellence & Fortune</p>
    <p class="text-red-600 font-medium mt-2 opacity-80">2024 Annual Raffle Results</p>
  </div>

  <!-- Winners Container -->
  <div class="max-w-6xl mx-auto">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
      <div
        *ngFor="let winner of winners; let i = index"
        class="winner-card winner-card-professional hover-lift pulse-glow"
        [style.animation-delay]="(i * 200) + 'ms'"
      >
        <!-- Winner Card -->
        <div class="rounded-3xl shadow-luxury border-luxury overflow-hidden shimmer-premium"
             [class]="'delay-' + ((i + 1) * 100)"
        >
          <!-- Rank Badge -->
          <div class="bg-gradient-to-r from-red-600 via-red-700 to-red-800 text-white text-center py-6 relative glass-effect">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-15"></div>
            <div class="text-2xl font-black text-luxury relative z-10 tracking-wide">
              {{ winner.rankEmoji }} {{ winner.rank }}{{ getOrdinalSuffix(winner.rank) }} PLACE
            </div>
          </div>

          <!-- Winner Content -->
          <div class="p-8 text-center bg-gradient-to-b from-white via-red-50/30 to-amber-50/20">
            <!-- Avatar/Initials -->
            <div class="w-28 h-28 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-luxury border-4 border-white pulse-glow">
              <span class="text-3xl font-black text-red-800 text-luxury">{{ winner.initials }}</span>
            </div>

            <!-- Winner Name -->
            <h3 class="text-3xl font-black gradient-text-premium text-luxury mb-6 tracking-tight">{{ winner.name }}</h3>

            <!-- Prize Image Placeholder -->
            <div class="w-32 h-32 bg-gradient-to-br from-yellow-100 via-yellow-200 to-yellow-300 rounded-2xl flex items-center justify-center mx-auto mb-6 border-luxury shadow-luxury hover:scale-110 transition-transform duration-500 pulse-glow">
              <span class="text-5xl drop-shadow-lg">{{ winner.emoji }}</span>
            </div>

            <!-- Prize Name -->
            <div class="glass-effect rounded-2xl p-6 border-luxury shadow-inner">
              <h4 class="text-xl font-black text-red-700 text-luxury mb-2">{{ winner.prize }}</h4>
              <div class="text-sm text-red-600 font-bold tracking-wide">
                � CONGRATULATIONS! �
              </div>
            </div>
          </div>

          <!-- Decorative Bottom Border with Pattern -->
          <div class="h-3 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 relative">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Confetti Animation Area -->
    <div class="text-center mb-8 relative">
      <div class="inline-flex space-x-4 text-3xl">
        <span class="sparkle text-yellow-500">✨</span>
        <span class="animate-bounce text-red-500" style="animation-delay: 0.2s">🎉</span>
        <span class="sparkle text-yellow-500" style="animation-delay: 0.4s">✨</span>
        <span class="animate-bounce text-red-500" style="animation-delay: 0.6s">🎊</span>
        <span class="sparkle text-yellow-500" style="animation-delay: 0.8s">✨</span>
        <span class="animate-bounce text-red-500" style="animation-delay: 1s">🎉</span>
        <span class="sparkle text-yellow-500" style="animation-delay: 1.2s">✨</span>
      </div>
      <!-- Floating celebration text -->
      <div class="mt-4">
        <span class="text-2xl font-bold bg-gradient-to-r from-red-600 to-yellow-600 bg-clip-text text-transparent celebration-text">
          🏆 CELEBRATION TIME! 🏆
        </span>
      </div>
    </div>

    <!-- Professional Footer Message -->
    <div class="text-center glass-effect rounded-3xl shadow-luxury p-12 border-luxury pulse-glow relative overflow-hidden">
      <!-- Premium Background decoration -->
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-yellow-100/30 to-red-100/30 pointer-events-none"></div>

      <div class="relative z-10">
        <div class="text-7xl mb-6 float">🎊</div>
        <p class="text-2xl font-black gradient-text-premium text-luxury mb-4 tracking-tight">
          Congratulations to all our winners!
        </p>
        <p class="text-red-600 font-bold text-xl mb-6 tracking-wide">
          More exciting prizes coming soon!
        </p>
        <div class="flex justify-center space-x-4 text-3xl">
          <span class="sparkle delay-100">🏮</span>
          <span class="sparkle delay-300">🎁</span>
          <span class="sparkle delay-500">🏮</span>
        </div>
      </div>
    </div>
  </div>
</div>
