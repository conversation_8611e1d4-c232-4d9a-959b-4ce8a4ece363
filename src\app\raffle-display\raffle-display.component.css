/* Winner Card Animations */
.winner-card {
  animation: slideInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 1.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced hover effects */
.winner-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 30px rgba(251, 191, 36, 0.3);
}

.winner-card:hover .bg-gradient-to-br {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

/* Pulse effect for rank badges */
.winner-card:nth-child(1) .bg-gradient-to-r {
  animation: goldPulse 2s ease-in-out infinite;
}

@keyframes goldPulse {
  0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.5); }
  50% { box-shadow: 0 0 30px rgba(251, 191, 36, 0.8); }
}

/* Shimmer effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Celebration text animation */
.celebration-text {
  animation: celebrationBounce 2s ease-in-out infinite;
}

@keyframes celebrationBounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

/* Festive glow effects */
.glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.glow-red {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

/* Sparkle animation */
.sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

/* Floating animation */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Custom border width */
.border-3 {
  border-width: 3px;
}

/* Custom shadow effects */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* PROFESSIONAL ENHANCEMENTS */

/* Premium Glass Effect */
.glass-effect {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Professional Gradient Text */
.gradient-text-premium {
  background: linear-gradient(135deg, #dc2626, #ea580c, #d97706, #ca8a04);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Enhanced Professional Shadows */
.shadow-4xl {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.shadow-luxury {
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.25),
    0 16px 40px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Professional Hover Lift */
.hover-lift {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow:
    0 40px 80px -12px rgba(0, 0, 0, 0.3),
    0 20px 50px rgba(0, 0, 0, 0.2);
}

/* Premium Card Styling */
.premium-card {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

/* Professional Pulse Glow */
.pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6), 0 0 60px rgba(251, 191, 36, 0.3);
  }
}

/* Enhanced Shimmer for Premium Look */
.shimmer-premium::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    rgba(251, 191, 36, 0.3),
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: shimmerPremium 4s ease-in-out infinite;
}

@keyframes shimmerPremium {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Professional Typography */
.text-luxury {
  font-weight: 800;
  letter-spacing: -0.025em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Premium Border Styles */
.border-luxury {
  border: 2px solid;
  border-image: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24) 1;
}

/* Professional Animation Delays */
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }

/* Enhanced Winner Card Professional Styling */
.winner-card-professional {
  background: linear-gradient(145deg, #ffffff 0%, #fefefe 50%, #f9fafb 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow:
    0 20px 40px -12px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.winner-card-professional:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 32px 64px -12px rgba(0, 0, 0, 0.25),
    0 16px 40px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(251, 191, 36, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}
